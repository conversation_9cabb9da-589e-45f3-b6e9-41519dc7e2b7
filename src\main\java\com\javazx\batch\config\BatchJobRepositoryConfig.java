package com.javazx.batch.config;

import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.support.SimpleJobLauncher;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.repository.support.JobRepositoryFactoryBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * Spring Batch Job Repository配置
 * 用于解决并发任务的死锁问题
 */
@Configuration
public class BatchJobRepositoryConfig {

    /**
     * 为Spring Batch创建专用的事务管理器
     * 使用独立的事务管理器可以减少死锁风险
     */
    @Bean("batchTransactionManager")
    public PlatformTransactionManager batchTransactionManager(@Qualifier("zhihui") DataSource dataSource) {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager();
        transactionManager.setDataSource(dataSource);

        transactionManager.setDefaultTimeout(30);

        return transactionManager;
    }

    /**
     * 自定义JobRepository配置
     * 优化并发性能和死锁处理
     */
    @Bean
    @Primary
    public JobRepository jobRepository(@Qualifier("zhihui") DataSource dataSource,
                                     @Qualifier("batchTransactionManager") PlatformTransactionManager transactionManager)
                                     throws Exception {
        JobRepositoryFactoryBean factory = new JobRepositoryFactoryBean();
        factory.setDataSource(dataSource);
        factory.setTransactionManager(transactionManager);

        // 设置表前缀
        factory.setTablePrefix("BATCH_");

        // 设置最大varchar长度
        factory.setMaxVarCharLength(1000);

        // 设置隔离级别为READ_COMMITTED，减少锁竞争
        factory.setIsolationLevelForCreate("ISOLATION_READ_COMMITTED");

        factory.afterPropertiesSet();
        return factory.getObject();
    }

    /**
     * 自定义JobLauncher配置
     * 使用专用的JobRepository，减少并发冲突
     */
    @Bean
    @Primary
    public JobLauncher jobLauncher(JobRepository jobRepository) throws Exception {
        SimpleJobLauncher jobLauncher = new SimpleJobLauncher();
        jobLauncher.setJobRepository(jobRepository);

        // 使用同步执行，避免并发问题
        jobLauncher.setTaskExecutor(new SimpleAsyncTaskExecutor());

        jobLauncher.afterPropertiesSet();
        return jobLauncher;
    }
}

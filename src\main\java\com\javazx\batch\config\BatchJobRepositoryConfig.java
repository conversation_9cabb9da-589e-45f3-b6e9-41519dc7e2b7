package com.javazx.batch.config;

import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.repository.support.JobRepositoryFactoryBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * Spring Batch Job Repository配置
 * 用于解决并发任务的死锁问题
 */
@Configuration
public class BatchJobRepositoryConfig {

    /**
     * 为Spring Batch创建专用的事务管理器
     * 使用独立的事务管理器可以减少死锁风险
     */
    @Bean("batchTransactionManager")
    public PlatformTransactionManager batchTransactionManager(@Qualifier("dataSource") DataSource dataSource) {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager();
        transactionManager.setDataSource(dataSource);
        
        // 设置事务隔离级别为READ_COMMITTED，减少锁竞争
        transactionManager.setDefaultTimeout(30); // 30秒超时
        
        return transactionManager;
    }

    /**
     * 自定义JobRepository配置
     * 优化并发性能和死锁处理
     */
    @Bean
    @Primary
    public JobRepository jobRepository(@Qualifier("dataSource") DataSource dataSource,
                                     @Qualifier("batchTransactionManager") PlatformTransactionManager transactionManager) 
                                     throws Exception {
        JobRepositoryFactoryBean factory = new JobRepositoryFactoryBean();
        factory.setDataSource(dataSource);
        factory.setTransactionManager(transactionManager);
        
        // 设置表前缀（如果需要）
        factory.setTablePrefix("BATCH_");
        
        // 设置最大varchar长度
        factory.setMaxVarCharLength(1000);
        
        // 启用序列化策略优化
        factory.setSerializer(null); // 使用默认序列化
        
        // 设置隔离级别
        factory.setIsolationLevelForCreate("ISOLATION_READ_COMMITTED");
        
        factory.afterPropertiesSet();
        return factory.getObject();
    }
}

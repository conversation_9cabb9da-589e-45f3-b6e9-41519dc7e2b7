package com.javazx.batch.job;

import cn.hutool.core.lang.UUID;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionException;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.configuration.JobLocator;
import org.springframework.batch.core.launch.JobLauncher;

@Slf4j
@Data
public class UserBatchJob {
	private String jobName;

	private JobLocator jobLocator;

	private JobLauncher jobLauncher;

	public void performJob() {
		int maxRetries = 3;
		int retryDelay = 5000; // 5秒

		for (int attempt = 1; attempt <= maxRetries; attempt++) {
			try {
				JobExecution result = jobLauncher.run(jobLocator.getJob(jobName),
						new JobParametersBuilder()
								.addString("jobKey", UUID.randomUUID().toString())
								.addLong("timestamp", System.currentTimeMillis()) // 添加时间戳确保唯一性
								.toJobParameters());
				log.info("批处理任务执行完成: {}, 状态: {}, 尝试次数: {}", jobName, result.getStatus(), attempt);
				return; // 成功执行，退出重试循环

			} catch (org.springframework.dao.DeadlockLoserDataAccessException ex) {
				log.warn("任务 {} 第 {} 次执行遇到死锁，准备重试", jobName, attempt);
				if (attempt < maxRetries) {
					try {
						Thread.sleep(retryDelay * attempt); // 递增延迟
					} catch (InterruptedException ie) {
						Thread.currentThread().interrupt();
						log.error("重试等待被中断: {}", jobName);
						return;
					}
				} else {
					log.error("任务 {} 重试 {} 次后仍然失败，放弃执行", jobName, maxRetries);
					throw ex;
				}
			} catch (JobExecutionException ex) {
				log.error("执行批处理任务失败: {}", jobName, ex);
				return; // 非死锁异常，不重试
			} catch (Exception ex) {
				log.error("查找或执行批处理任务时发生错误: {}", jobName, ex);
				return; // 其他异常，不重试
			}
		}
	}

}

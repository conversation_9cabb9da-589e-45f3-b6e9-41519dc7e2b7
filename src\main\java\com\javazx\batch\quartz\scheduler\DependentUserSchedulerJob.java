package com.javazx.batch.quartz.scheduler;

import com.javazx.batch.config.properties.TaskConfig;
import com.javazx.batch.job.UserBatchJob;
import com.javazx.batch.quartz.ApplicationContextUtil;
import com.javazx.batch.service.TaskDependencyManager;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.util.concurrent.locks.ReentrantLock;

/**
 * 支持任务依赖的定时任务调度器
 * 可以处理任务间的依赖关系，确保依赖任务完成后再执行当前任务
 */
@DisallowConcurrentExecution
public class DependentUserSchedulerJob extends QuartzJobBean {
    private static final Logger logger = LoggerFactory.getLogger(DependentUserSchedulerJob.class);

    private String batchJob;
    private String dependencyTask;
    private int maxWaitMinutes;

    public void setBatchJob(String batchJob) {
        this.batchJob = batchJob;
    }

    public void setDependencyTask(String dependencyTask) {
        this.dependencyTask = dependencyTask;
    }

    public void setMaxWaitMinutes(int maxWaitMinutes) {
        this.maxWaitMinutes = maxWaitMinutes;
    }

    @Override
    protected void executeInternal(JobExecutionContext context) {
        logger.info("支持依赖的定时执行任务开始，任务: {}, 依赖任务: {}, 开始时间: {}", 
                   batchJob, dependencyTask, context.getFireTime());

        ApplicationContext applicationContext = ApplicationContextUtil.getApplicationContext();
        UserBatchJob job = applicationContext.getBean(batchJob, UserBatchJob.class);
        TaskConfig taskConfig = applicationContext.getBean(TaskConfig.class);
        TaskDependencyManager dependencyManager = applicationContext.getBean(TaskDependencyManager.class);

        logger.info("定时执行任务开始：{}, 执行任务配置: {}", job, taskConfig.getTask(job.getJobName()));

        try {
            // 如果有依赖任务，需要等待依赖任务完成
            if (dependencyTask != null && !dependencyTask.trim().isEmpty()) {
                logger.info("任务 {} 依赖于任务 {}，开始检查依赖状态", batchJob, dependencyTask);

                dependencyManager.clearDependencyCompletionStatus(dependencyTask);

                // 检查依赖任务是否已完成
                if (!dependencyManager.isDependencyTaskCompleted(dependencyTask)) {
                    logger.info("依赖任务 {} 尚未完成，开始等待...", dependencyTask);

                    // 等待依赖任务完成
                    boolean dependencyCompleted = dependencyManager.waitForDependencyTask(dependencyTask, maxWaitMinutes);

                    if (!dependencyCompleted) {
                        logger.error("依赖任务 {} 在 {} 分钟内未完成，跳过当前任务 {} 的执行",
                                   dependencyTask, maxWaitMinutes, batchJob);
                        return;
                    }
                } else {
                    logger.info("依赖任务 {} 已完成，可以执行当前任务 {}", dependencyTask, batchJob);
                }
            }

            // 标记当前任务开始执行
            String executionId = dependencyManager.markTaskStarted(batchJob);

            try {
                // 执行任务
                job.performJob();

                // 标记任务成功完成
                dependencyManager.markTaskCompleted(batchJob, executionId, true);

                logger.info("任务 {} 执行成功完成", batchJob);

            } catch (Exception jobException) {
                logger.error("任务 {} 执行失败: {}", batchJob, jobException.getMessage(), jobException);

                // 标记任务失败
                dependencyManager.markTaskCompleted(batchJob, executionId, false);
            }

        } catch (Exception exception) {
            logger.error("任务 {} 启动失败: {}", batchJob, exception.getMessage(), exception);
        }

        logger.info("定时执行任务结束，任务: {}", batchJob);
    }
}

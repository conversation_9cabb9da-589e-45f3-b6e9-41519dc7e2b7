package com.javazx.batch.quartz.scheduler;

import com.javazx.batch.config.properties.TaskConfig;
import com.javazx.batch.job.UserBatchJob;
import com.javazx.batch.quartz.ApplicationContextUtil;
import com.javazx.batch.service.TaskDependencyManager;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.util.concurrent.locks.ReentrantLock;

@DisallowConcurrentExecution
public class UserSchedulerJob extends QuartzJobBean{
	private static final Logger logger = LoggerFactory.getLogger(UserSchedulerJob.class);

	// 全局任务执行锁，确保Spring Batch任务串行执行
	private static final ReentrantLock BATCH_EXECUTION_LOCK = new ReentrantLock();

	private String batchJob;
	
	public void setBatchJob(String batchJob){
		this.batchJob = batchJob;
	}
	
	@Override
	protected void executeInternal(JobExecutionContext context){
		logger.info("定时执行任务开始时间"+ context.getFireTime());

		BATCH_EXECUTION_LOCK.lock();
		try {
			// 从Spring容器中获取UserBatchJob实例
			ApplicationContext applicationContext = ApplicationContextUtil.getApplicationContext();
			UserBatchJob job = applicationContext.getBean(batchJob, UserBatchJob.class);
			TaskConfig taskConfig = applicationContext.getBean(TaskConfig.class);
			TaskDependencyManager dependencyManager = applicationContext.getBean(TaskDependencyManager.class);

			logger.info("定时执行任务开始："+ job + "  执行任务配置" + taskConfig.getTask(job.getJobName()));

			try{
				// 标记任务开始执行
				dependencyManager.markTaskStarted(batchJob);

				// 执行任务
				job.performJob();

				// 标记任务成功完成
				dependencyManager.markTaskCompleted(batchJob, true);

			}catch(Exception exception){
				logger.error("任务 "+ batchJob+" 没有被执行 : "+ exception.getMessage());

				// 标记任务失败
				dependencyManager.markTaskCompleted(batchJob, false);
			}
		} finally {
			// 确保锁被释放
			BATCH_EXECUTION_LOCK.unlock();
		}
		logger.info("定时执行任务结束");
	}
}
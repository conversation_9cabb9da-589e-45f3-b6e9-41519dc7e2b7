package com.javazx.batch.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 任务依赖管理器
 * 负责管理任务间的依赖关系，确保医护同步在患者同步之前完成
 */
@Slf4j
@Service
public class TaskDependencyManager {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final String TASK_COMPLETION_PREFIX = "task:completion:";
    private static final String TASK_EXECUTION_PREFIX = "task:execution:";
    private static final String CURRENT_EXECUTION_PREFIX = "task:current:";
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    /**
     * 标记任务开始执行
     * @param taskName 任务名称
     * @return 返回本次执行的唯一标识符
     */
    public String markTaskStarted(String taskName) {
        String executionId = UUID.randomUUID().toString();
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);

        // 清理该任务之前的完成状态，确保新的执行周期
        clearPreviousCompletionStatus(taskName);

        // 使用执行ID作为唯一标识
        String executionKey = TASK_EXECUTION_PREFIX + taskName + ":" + executionId;
        String currentKey = CURRENT_EXECUTION_PREFIX + taskName;

        // 设置执行状态，15分钟后自动过期
        redisTemplate.opsForValue().set(executionKey, "STARTED", 15, TimeUnit.MINUTES);
        // 设置当前执行ID，15分钟后自动过期
        redisTemplate.opsForValue().set(currentKey, executionId, 15, TimeUnit.MINUTES);

        log.info("任务 {} 开始执行，执行ID: {}, 时间: {}", taskName, executionId, currentTime);
        return executionId;
    }

    /**
     * 标记任务完成
     * @param taskName 任务名称
     * @param executionId 本次执行的唯一标识符
     * @param success 是否成功
     */
    public void markTaskCompleted(String taskName, String executionId, boolean success) {
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);
        String status = success ? "SUCCESS" : "FAILED";

        // 使用执行ID作为唯一标识
        String completionKey = TASK_COMPLETION_PREFIX + taskName + ":" + executionId;

        // 设置完成状态，15分钟后自动过期
        redisTemplate.opsForValue().set(completionKey, status, 15, TimeUnit.MINUTES);

        // 执行状态会自动过期删除，当前执行ID保留用于依赖检查，也会自动过期
        log.info("任务 {} 执行完成，执行ID: {}, 状态: {}, 时间: {}", taskName, executionId, status, currentTime);
    }

    /**
     * 标记任务完成（向后兼容方法）
     * @param taskName 任务名称
     * @param success 是否成功
     */
    public void markTaskCompleted(String taskName, boolean success) {
        // 生成一个临时的执行ID用于兼容性
        String tempExecutionId = "manual-" + UUID.randomUUID().toString();
        markTaskCompleted(taskName, tempExecutionId, success);
    }

    /**
     * 检查依赖任务是否完成（基于当前执行ID）
     * @param dependencyTaskName 依赖的任务名称
     * @return 是否完成
     */
    public boolean isDependencyTaskCompleted(String dependencyTaskName) {
        try {
            String currentKey = CURRENT_EXECUTION_PREFIX + dependencyTaskName;
            String currentExecutionId = redisTemplate.opsForValue().get(currentKey);

            if (currentExecutionId != null) {
                // 检查当前执行ID对应的完成状态
                String completionKey = TASK_COMPLETION_PREFIX + dependencyTaskName + ":" + currentExecutionId;
                String status = redisTemplate.opsForValue().get(completionKey);

                if ("SUCCESS".equals(status)) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("检查依赖任务 {} 完成状态时发生错误: {}", dependencyTaskName, e.getMessage());
        }

        return false;
    }

    /**
     * 检查依赖任务是否正在执行（基于当前执行ID）
     * @param dependencyTaskName 依赖的任务名称
     * @return 是否正在执行
     */
    public boolean isDependencyTaskRunning(String dependencyTaskName) {
        // 检查是否有当前执行的任务
        String currentKey = CURRENT_EXECUTION_PREFIX + dependencyTaskName;
        String currentExecutionId = redisTemplate.opsForValue().get(currentKey);

        if (currentExecutionId != null) {
            // 检查对应的执行状态
            String executionKey = TASK_EXECUTION_PREFIX + dependencyTaskName + ":" + currentExecutionId;
            String status = redisTemplate.opsForValue().get(executionKey);

            if ("STARTED".equals(status)) {
                return true;
            }
        }

        log.info("检查依赖任务 {} 运行状态: false", dependencyTaskName);
        return false;
    }

    /**
     * 等待依赖任务完成
     * @param dependencyTaskName 依赖的任务名称
     * @param maxWaitMinutes 最大等待时间（分钟）
     * @return 是否在等待时间内完成
     */
    public boolean waitForDependencyTask(String dependencyTaskName, int maxWaitMinutes) {
        log.info("开始等待依赖任务 {} 完成，最大等待时间: {} 分钟", dependencyTaskName, maxWaitMinutes);
        
        long startTime = System.currentTimeMillis();
        long maxWaitTime = maxWaitMinutes * 60 * 1000; // 转换为毫秒
        
        while (System.currentTimeMillis() - startTime < maxWaitTime) {
            // 检查是否已完成
            if (isDependencyTaskCompleted(dependencyTaskName)) {
                log.info("依赖任务 {} 已完成", dependencyTaskName);
                return true;
            }
            
            // 检查是否正在运行
            if (isDependencyTaskRunning(dependencyTaskName)) {
                log.info("依赖任务 {} 正在运行，继续等待...", dependencyTaskName);
            } else {
                log.warn("依赖任务 {} 未在运行且未完成，可能存在问题", dependencyTaskName);
            }
            
            try {
                Thread.sleep(10000); // 等待10秒后再次检查
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("等待依赖任务时被中断", e);
                return false;
            }
        }
        
        log.warn("等待依赖任务 {} 超时，最大等待时间: {} 分钟", dependencyTaskName, maxWaitMinutes);
        return false;
    }

    /**
     * 清理过期的任务状态
     */
    public void cleanupExpiredTaskStatus() {
        log.info("清理过期的任务状态");
    }

    /**
     * 清理指定任务之前的完成状态
     * 确保新的执行周期不会受到之前完成状态的影响
     * @param taskName 任务名称
     */
    private void clearPreviousCompletionStatus(String taskName) {
        try {
            String currentKey = CURRENT_EXECUTION_PREFIX + taskName;
            String previousExecutionId = redisTemplate.opsForValue().get(currentKey);

            if (previousExecutionId != null) {
                // 清理之前的完成状态
                String previousCompletionKey = TASK_COMPLETION_PREFIX + taskName + ":" + previousExecutionId;
                redisTemplate.delete(previousCompletionKey);

                log.info("清理任务 {} 上一次完成状态，执行ID: {}", taskName, previousExecutionId);
            }
        } catch (Exception e) {
            log.error("清理任务 {} 上一次完成状态时发生错误: {}", taskName, e.getMessage());
        }
    }

    /**
     * 清理依赖任务的完成状态，确保执行周期同步
     * @param dependencyTaskName 依赖任务名称
     */
    public void clearDependencyCompletionStatus(String dependencyTaskName) {
        try {
            String currentKey = CURRENT_EXECUTION_PREFIX + dependencyTaskName;
            String previousExecutionId = redisTemplate.opsForValue().get(currentKey);

            if (previousExecutionId != null) {
                // 清理依赖任务的完成状态
                String previousCompletionKey = TASK_COMPLETION_PREFIX + dependencyTaskName + ":" + previousExecutionId;
                redisTemplate.delete(previousCompletionKey);

                log.info("清理依赖任务 {} 的完成状态，确保执行周期同步，执行ID: {}", dependencyTaskName, previousExecutionId);
            }
        } catch (Exception e) {
            log.error("清理依赖任务 {} 完成状态时发生错误: {}", dependencyTaskName, e.getMessage());
        }
    }
}

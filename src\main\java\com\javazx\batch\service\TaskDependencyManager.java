package com.javazx.batch.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 任务依赖管理器
 * 负责管理任务间的依赖关系，确保医护同步在患者同步之前完成
 */
@Slf4j
@Service
public class TaskDependencyManager {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final String TASK_COMPLETION_PREFIX = "task:completion:";
    private static final String TASK_EXECUTION_PREFIX = "task:execution:";
    private static final String CURRENT_EXECUTION_PREFIX = "task:current:";
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    /**
     * 标记任务开始执行
     * @param taskName 任务名称
     * @return 返回本次执行的唯一标识符
     */
    public String markTaskStarted(String taskName) {
        String executionId = UUID.randomUUID().toString();
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);

        // 清理上一次任务的执行状态（如果存在）
        clearPreviousTaskExecution(taskName);

        // 使用执行ID作为唯一标识
        String executionKey = TASK_EXECUTION_PREFIX + taskName + ":" + executionId;
        String currentKey = CURRENT_EXECUTION_PREFIX + taskName;

        // 设置执行状态
        redisTemplate.opsForValue().set(executionKey, "STARTED", 15, TimeUnit.MINUTES);
        // 设置当前执行ID
        redisTemplate.opsForValue().set(currentKey, executionId, 15, TimeUnit.MINUTES);

        log.info("任务 {} 开始执行，执行ID: {}, 时间: {}", taskName, executionId, currentTime);
        return executionId;
    }

    /**
     * 标记任务完成
     * @param taskName 任务名称
     * @param executionId 本次执行的唯一标识符
     * @param success 是否成功
     */
    public void markTaskCompleted(String taskName, String executionId, boolean success) {
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);
        String status = success ? "SUCCESS" : "FAILED";

        // 使用执行ID作为唯一标识
        String completionKey = TASK_COMPLETION_PREFIX + taskName + ":" + executionId;
        String executionKey = TASK_EXECUTION_PREFIX + taskName + ":" + executionId;

        // 清理该任务的所有历史完成状态
        clearTaskCompletionHistory(taskName);

        // 设置当前完成状态
        redisTemplate.opsForValue().set(completionKey, status, 15, TimeUnit.MINUTES);

        // 只清除执行状态，保留当前执行ID用于依赖检查
        redisTemplate.delete(executionKey);
        // 注意：不删除 currentKey，保留当前执行ID直到下次任务开始

        log.info("任务 {} 执行完成，执行ID: {}, 状态: {}, 时间: {}", taskName, executionId, status, currentTime);
    }

    /**
     * 标记任务完成（向后兼容方法）
     * @param taskName 任务名称
     * @param success 是否成功
     */
    public void markTaskCompleted(String taskName, boolean success) {
        // 生成一个临时的执行ID用于兼容性
        String tempExecutionId = "manual-" + UUID.randomUUID().toString();
        markTaskCompleted(taskName, tempExecutionId, success);
    }

    /**
     * 检查依赖任务是否完成（基于当前执行ID）
     * @param dependencyTaskName 依赖的任务名称
     * @return 是否完成
     */
    public boolean isDependencyTaskCompleted(String dependencyTaskName) {
        try {
            String currentKey = CURRENT_EXECUTION_PREFIX + dependencyTaskName;
            String currentExecutionId = redisTemplate.opsForValue().get(currentKey);

            if (currentExecutionId != null) {
                // 检查当前执行ID对应的完成状态
                String completionKey = TASK_COMPLETION_PREFIX + dependencyTaskName + ":" + currentExecutionId;
                String status = redisTemplate.opsForValue().get(completionKey);

                if ("SUCCESS".equals(status)) {
                    log.info("检查依赖任务 {} 完成状态: true, 当前执行ID: {}", dependencyTaskName, currentExecutionId);
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("检查依赖任务 {} 完成状态时发生错误: {}", dependencyTaskName, e.getMessage());
        }

        log.info("检查依赖任务 {} 完成状态: false", dependencyTaskName);
        return false;
    }

    /**
     * 检查依赖任务是否正在执行（基于当前执行ID）
     * @param dependencyTaskName 依赖的任务名称
     * @return 是否正在执行
     */
    public boolean isDependencyTaskRunning(String dependencyTaskName) {
        // 检查是否有当前执行的任务
        String currentKey = CURRENT_EXECUTION_PREFIX + dependencyTaskName;
        String currentExecutionId = redisTemplate.opsForValue().get(currentKey);

        if (currentExecutionId != null) {
            // 检查对应的执行状态
            String executionKey = TASK_EXECUTION_PREFIX + dependencyTaskName + ":" + currentExecutionId;
            String status = redisTemplate.opsForValue().get(executionKey);

            if ("STARTED".equals(status)) {
                log.info("检查依赖任务 {} 运行状态: true, 执行ID: {}", dependencyTaskName, currentExecutionId);
                return true;
            }
        }

        log.info("检查依赖任务 {} 运行状态: false", dependencyTaskName);
        return false;
    }

    /**
     * 等待依赖任务完成
     * @param dependencyTaskName 依赖的任务名称
     * @param maxWaitMinutes 最大等待时间（分钟）
     * @return 是否在等待时间内完成
     */
    public boolean waitForDependencyTask(String dependencyTaskName, int maxWaitMinutes) {
        log.info("开始等待依赖任务 {} 完成，最大等待时间: {} 分钟", dependencyTaskName, maxWaitMinutes);
        
        long startTime = System.currentTimeMillis();
        long maxWaitTime = maxWaitMinutes * 60 * 1000; // 转换为毫秒
        
        while (System.currentTimeMillis() - startTime < maxWaitTime) {
            // 检查是否已完成
            if (isDependencyTaskCompleted(dependencyTaskName)) {
                log.info("依赖任务 {} 已完成", dependencyTaskName);
                return true;
            }
            
            // 检查是否正在运行
            if (isDependencyTaskRunning(dependencyTaskName)) {
                log.info("依赖任务 {} 正在运行，继续等待...", dependencyTaskName);
            } else {
                log.warn("依赖任务 {} 未在运行且未完成，可能存在问题", dependencyTaskName);
            }
            
            try {
                Thread.sleep(10000); // 等待10秒后再次检查
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("等待依赖任务时被中断", e);
                return false;
            }
        }
        
        log.warn("等待依赖任务 {} 超时，最大等待时间: {} 分钟", dependencyTaskName, maxWaitMinutes);
        return false;
    }

    /**
     * 清理过期的任务状态
     */
    public void cleanupExpiredTaskStatus() {
        log.info("开始清理过期的任务状态");

        try {
            cleanupTaskKeys(TASK_COMPLETION_PREFIX + "*");

            cleanupTaskKeys(TASK_EXECUTION_PREFIX + "*");

            cleanupTaskKeys(CURRENT_EXECUTION_PREFIX + "*");

            log.info("清理过期的任务状态完成");
        } catch (Exception e) {
            log.error("清理过期任务状态时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 清理上一次任务的执行状态
     * @param taskName 任务名称
     */
    private void clearPreviousTaskExecution(String taskName) {
        try {
            String currentKey = CURRENT_EXECUTION_PREFIX + taskName;
            String previousExecutionId = redisTemplate.opsForValue().get(currentKey);

            if (previousExecutionId != null) {
                // 清理上一次的执行状态和完成状态
                String previousExecutionKey = TASK_EXECUTION_PREFIX + taskName + ":" + previousExecutionId;
                String previousCompletionKey = TASK_COMPLETION_PREFIX + taskName + ":" + previousExecutionId;

                redisTemplate.delete(previousExecutionKey);
                redisTemplate.delete(previousCompletionKey);

                log.info("清理任务 {} 上一次执行状态，执行ID: {}", taskName, previousExecutionId);
            }
        } catch (Exception e) {
            log.error("清理任务 {} 上一次执行状态时发生错误: {}", taskName, e.getMessage());
        }
    }

    /**
     * 清理指定任务的所有历史完成状态
     * @param taskName 任务名称
     */
    private void clearTaskCompletionHistory(String taskName) {
        try {
            String pattern = TASK_COMPLETION_PREFIX + taskName + ":*";
            var keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("清理任务 {} 的历史完成状态，删除 {} 个键", taskName, keys.size());
            }
        } catch (Exception e) {
            log.error("清理任务 {} 历史完成状态时发生错误: {}", taskName, e.getMessage());
        }
    }

    /**
     * 清理指定模式的键
     * @param pattern 键模式
     */
    private void cleanupTaskKeys(String pattern) {
        try {
            var keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                log.info("找到 {} 个匹配模式 {} 的键", keys.size(), pattern);
            }
        } catch (Exception e) {
            log.error("清理键模式 {} 时发生错误: {}", pattern, e.getMessage());
        }
    }
}

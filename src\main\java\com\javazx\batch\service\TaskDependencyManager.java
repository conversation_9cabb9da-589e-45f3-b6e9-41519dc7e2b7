package com.javazx.batch.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * 任务依赖管理器
 * 负责管理任务间的依赖关系，确保医护同步在患者同步之前完成
 */
@Slf4j
@Service
public class TaskDependencyManager {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final String TASK_COMPLETION_PREFIX = "task:completion:";
    private static final String TASK_EXECUTION_PREFIX = "task:execution:";
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    /**
     * 标记任务开始执行
     * @param taskName 任务名称
     */
    public void markTaskStarted(String taskName) {
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);
        String key = TASK_EXECUTION_PREFIX + taskName + ":" + currentTime;
        
        redisTemplate.opsForValue().set(key, "STARTED", 10, TimeUnit.MINUTES);
        log.info("任务 {} 开始执行，时间: {}", taskName, currentTime);
    }

    /**
     * 标记任务完成
     * @param taskName 任务名称
     * @param success 是否成功
     */
    public void markTaskCompleted(String taskName, boolean success) {
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);
        String completionKey = TASK_COMPLETION_PREFIX + taskName + ":" + currentTime;
        String executionKey = TASK_EXECUTION_PREFIX + taskName + ":" + currentTime;
        
        String status = success ? "SUCCESS" : "FAILED";
        redisTemplate.opsForValue().set(completionKey, status, 10, TimeUnit.MINUTES);
        
        // 清除执行状态
        redisTemplate.delete(executionKey);
        
        log.info("任务 {} 执行完成，状态: {}, 时间: {}", taskName, status, currentTime);
    }

    /**
     * 检查依赖任务是否在当前时间窗口内完成
     * @param dependencyTaskName 依赖的任务名称
     * @return 是否完成
     */
    public boolean isDependencyTaskCompleted(String dependencyTaskName) {
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);
        String key = TASK_COMPLETION_PREFIX + dependencyTaskName + ":" + currentTime;
        
        String status = redisTemplate.opsForValue().get(key);
        boolean completed = "SUCCESS".equals(status);
        
        log.info("检查依赖任务 {} 完成状态: {}, 时间窗口: {}", dependencyTaskName, completed, currentTime);
        return completed;
    }

    /**
     * 检查依赖任务是否正在执行
     * @param dependencyTaskName 依赖的任务名称
     * @return 是否正在执行
     */
    public boolean isDependencyTaskRunning(String dependencyTaskName) {
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);
        String key = TASK_EXECUTION_PREFIX + dependencyTaskName + ":" + currentTime;

        String status = redisTemplate.opsForValue().get(key);
        boolean running = "STARTED".equals(status);

        log.info("检查依赖任务 {} 运行状态: {}, 时间窗口: {}", dependencyTaskName, running, currentTime);
        return running;
    }

    /**
     * 检查任务是否正在执行（通用方法）
     * @param taskName 任务名称
     * @return 是否正在执行
     */
    public boolean isTaskRunning(String taskName) {
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);
        String key = TASK_EXECUTION_PREFIX + taskName + ":" + currentTime;

        String status = redisTemplate.opsForValue().get(key);
        boolean running = "STARTED".equals(status);

        log.info("检查任务 {} 运行状态: {}, 时间窗口: {}", taskName, running, currentTime);
        return running;
    }

    /**
     * 等待依赖任务完成
     * @param dependencyTaskName 依赖的任务名称
     * @param maxWaitMinutes 最大等待时间（分钟）
     * @return 是否在等待时间内完成
     */
    public boolean waitForDependencyTask(String dependencyTaskName, int maxWaitMinutes) {
        log.info("开始等待依赖任务 {} 完成，最大等待时间: {} 分钟", dependencyTaskName, maxWaitMinutes);
        
        long startTime = System.currentTimeMillis();
        long maxWaitTime = maxWaitMinutes * 60 * 1000; // 转换为毫秒
        
        while (System.currentTimeMillis() - startTime < maxWaitTime) {
            // 检查是否已完成
            if (isDependencyTaskCompleted(dependencyTaskName)) {
                log.info("依赖任务 {} 已完成", dependencyTaskName);
                return true;
            }
            
            // 检查是否正在运行
            if (isDependencyTaskRunning(dependencyTaskName)) {
                log.info("依赖任务 {} 正在运行，继续等待...", dependencyTaskName);
            } else {
                log.warn("依赖任务 {} 未在运行且未完成，可能存在问题", dependencyTaskName);
            }
            
            try {
                Thread.sleep(10000); // 等待10秒后再次检查
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("等待依赖任务时被中断", e);
                return false;
            }
        }
        
        log.warn("等待依赖任务 {} 超时，最大等待时间: {} 分钟", dependencyTaskName, maxWaitMinutes);
        return false;
    }

    /**
     * 清理过期的任务状态
     */
    public void cleanupExpiredTaskStatus() {
        log.info("清理过期的任务状态");
    }
}

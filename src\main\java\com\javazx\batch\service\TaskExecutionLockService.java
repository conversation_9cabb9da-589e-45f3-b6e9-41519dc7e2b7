package com.javazx.batch.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 任务执行锁服务
 * 使用Redis分布式锁防止同一任务的并发执行
 */
@Slf4j
@Service
public class TaskExecutionLockService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final String LOCK_PREFIX = "task:lock:";
    private static final long DEFAULT_LOCK_TIMEOUT = 30; // 30分钟
    private static final TimeUnit LOCK_TIME_UNIT = TimeUnit.MINUTES;

    /**
     * 尝试获取任务执行锁
     * @param taskName 任务名称
     * @return 是否成功获取锁
     */
    public boolean tryLock(String taskName) {
        return tryLock(taskName, DEFAULT_LOCK_TIMEOUT, LOCK_TIME_UNIT);
    }

    /**
     * 尝试获取任务执行锁
     * @param taskName 任务名称
     * @param timeout 锁超时时间
     * @param timeUnit 时间单位
     * @return 是否成功获取锁
     */
    public boolean tryLock(String taskName, long timeout, TimeUnit timeUnit) {
        String lockKey = LOCK_PREFIX + taskName;
        String lockValue = String.valueOf(System.currentTimeMillis());
        
        try {
            Boolean success = redisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, timeout, timeUnit);
            if (Boolean.TRUE.equals(success)) {
                log.info("成功获取任务执行锁: {}", taskName);
                return true;
            } else {
                log.warn("获取任务执行锁失败，任务可能正在执行: {}", taskName);
                return false;
            }
        } catch (Exception e) {
            log.error("获取任务执行锁时发生异常: {}", taskName, e);
            return false;
        }
    }

    /**
     * 释放任务执行锁
     * @param taskName 任务名称
     */
    public void releaseLock(String taskName) {
        String lockKey = LOCK_PREFIX + taskName;
        
        try {
            Boolean deleted = redisTemplate.delete(lockKey);
            if (Boolean.TRUE.equals(deleted)) {
                log.info("成功释放任务执行锁: {}", taskName);
            } else {
                log.warn("释放任务执行锁失败，锁可能已过期: {}", taskName);
            }
        } catch (Exception e) {
            log.error("释放任务执行锁时发生异常: {}", taskName, e);
        }
    }

    /**
     * 检查任务是否被锁定
     * @param taskName 任务名称
     * @return 是否被锁定
     */
    public boolean isLocked(String taskName) {
        String lockKey = LOCK_PREFIX + taskName;
        
        try {
            Boolean exists = redisTemplate.hasKey(lockKey);
            return Boolean.TRUE.equals(exists);
        } catch (Exception e) {
            log.error("检查任务锁状态时发生异常: {}", taskName, e);
            return false;
        }
    }

    /**
     * 强制释放任务锁（谨慎使用）
     * @param taskName 任务名称
     */
    public void forceReleaseLock(String taskName) {
        log.warn("强制释放任务执行锁: {}", taskName);
        releaseLock(taskName);
    }
}

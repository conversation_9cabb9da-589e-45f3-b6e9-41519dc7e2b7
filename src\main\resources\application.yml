spring:
  application:
    name: dataBatch
  profiles:
    active: @profiles.active@
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  # 允许Bean定义覆盖，解决Spring Batch配置冲突
  main:
    allow-bean-definition-overriding: true
  # 事务管理优化
  transaction:
    default-timeout: 30
    rollback-on-commit-failure: true
  datasource:
    dynamic:
      primary: zhihui
      datasource:
        zhihui:
          url: ****************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: root
          password: 123456
          type: com.alibaba.druid.pool.DruidDataSource
        hzzyy:
          url: **********************************************
          driver-class-name: oracle.jdbc.driver.OracleDriver
          username: mzpzjk
          password: L5psKupGt8
          type: com.alibaba.druid.pool.DruidDataSource
      # wzsrmyyWithInspectionReportOracle 数据源（已注释）
      #wzsrmyyWithInspectionReportOracle:
      #  url: ******************************************
      #  driver-class-name: oracle.jdbc.driver.OracleDriver
      #  username: link_web
      #  password: lkoa6
      #  type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 连接池配置优化，减少死锁风险
      initial-size: 5
      min-idle: 5
      max-active: 20  # 减少最大连接数，避免过多并发
      max-wait: 30000  # 减少等待时间
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 连接泄漏检测
      remove-abandoned: true
      remove-abandoned-timeout: 1800
      log-abandoned: true
      # 事务相关优化
      default-auto-commit: false
      default-transaction-isolation: 2  # READ_COMMITTED
  batch:
    jdbc:
      initialize-schema: always
      # Spring Batch数据库优化配置
      isolation-level-for-create: ISOLATION_READ_COMMITTED
      table-prefix: BATCH_
    job:
      enabled: false
    # 任务执行优化配置
    task:
      execution:
        pool:
          core-size: 2
          max-size: 5
          queue-capacity: 100
          keep-alive: 60s
  redis:
    host: 127.0.0.1
    port: 6379
    password:
    database: 12
    lettuce:
      pool:
        max-active: 8
        max-wait: 10
        max-idle: 8
        min-idle: 1
    timeout: 30S
  # Jackson配置（已注释）
  #jackson:
  #  default-property-inclusion: NON_NULL
  #  property-naming-strategy: SNAKE_CASE

server:
  port: 8888

hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: false

ribbon:
  ReadTimeout: 60000
  ConnectTimeout: 60000

mybatis-plus:
  mapper-locations: classpath:/mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
    jdbc-type-for-null: NULL
  global-config:
    banner: false

# Spring Batch配置

# SQL日志级别
logging:
  level:
    com: INFO